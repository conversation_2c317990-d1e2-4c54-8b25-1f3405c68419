

public class StorageMgr
{
    private static readonly string Key_SoundEffectOn = "Key_SoundEffectOn";
    private static readonly string Key_VibrationOn = "Key_VibrationOn";
    private static readonly string Key_GameProgress = "Key_GameProgress";
    private static readonly string Key_NeedGuide = "Key_NeedGuide";
    private static readonly string Key_NeedGuideFactory = "Key_NeedGuideFactory";
    private static readonly string Key_Num = "Key_Num";
    private static readonly string Key_NewPlayer = "Key_NewPlayer";
    private static readonly string Key_ClientId = "Key_ClientId";
    private static readonly string Key_OpenId = "Key_OpenId";
    private static readonly string Key_Heart = "Key_Heart";
    private static readonly string Key_Level = "Key_Level";
    private static readonly string Key_Star = "Key_Star";

    private static readonly string Key_Gold = "Key_Gold";
    private static readonly string Key_CooldownTime = "Key_CooldownTime";


    private static readonly string ItemBulb = "ItemBulb";
    private static readonly string ItemShuffle = "ItemShuffle";
    private static readonly string ItemTurn = "ItemTurn";
    private static readonly string ItemMagnet = "ItemMagnet";
    private static readonly string KeyGetFreeItemCount = "KeyGetFreeItemCount";
    private static readonly string KeyPassLevelIndex = "KeyPassLevelIndex";
    private static readonly string KeyPassLevel = "KeyPassLevel";
    private static readonly string KeyPigGold = "KeyPigGold";
    private static readonly string KeyVolumeBgm = "KeyVolumeBgm";
    private static readonly string KeyVolumeEffect = "KeyVolumeEffect";
    private static readonly string KeyPlayLevel = "KeyPlayLevel";
    private static readonly string KeyChannel = "KeyChannel";
    private static readonly string KeyOpenSlotByShareCount = "KeyOpenSlotByShareCount";
    private static readonly string KeyLevelRestartCount = "KeyLevelRestartCount";
    private static readonly string Editor_Nums = "Editor_Nums";
    private static readonly string Editor_Delay = "Editor_Delay";
    public static string EditorNums
    {
        get
        {
            return GetString(Editor_Nums, "");
        }
        set
        {
            SetString(Editor_Nums, value);
        }
    }
    public static string EditorDelay
    {
        get
        {
            return GetString(Editor_Delay, "");
        }
        set
        {
            SetString(Editor_Delay, value);
        }
    }

    public static int GetLevelPlayCount(int level)
    {
        return GetInt(KeyPlayLevel + level.ToString(), 0);
    }
    public static void SetLevelPlayCount(int level, int count)
    {
        SetInt(KeyPlayLevel + level.ToString(), count);
    }

    public static int GetLevelRestartCount(int level)
    {
        return GetInt(KeyLevelRestartCount + level.ToString(), 0);
    }
    public static void SetLevelRestartCount(int level, int count)
    {
        SetInt(KeyLevelRestartCount + level.ToString(), count);
    }

    public static int PigGold { get { return GetInt(KeyPigGold, 0); } set { SetInt(KeyPigGold, value); } }
    public static int PassLevelIndex { get { return GetInt(KeyPassLevelIndex, 0); } set { SetInt(KeyPassLevelIndex, value); } }
    public static int PassLevel { get { return GetInt(KeyPassLevel, 0); } set { SetInt(KeyPassLevel, value); } }

    public static int GetHeart(int defaultValue)
    {
        return GetInt(Key_Heart, defaultValue);
    }
    public static void SetHeart(int value)
    {
        SetInt(Key_Heart, value);
    }
    public static int HeartCooldownSecondTicks
    {
        get
        {
            return GetInt(Key_CooldownTime, 0);
        }
        set
        {
            SetInt(Key_CooldownTime, value);
        }
    }

    public static int Level
    {
        get
        {
            return GetInt(Key_Level, 1);
        }
        set
        {
            SetInt(Key_Level, value);
        }
    }

    public static int Star
    {
        get
        {
            return GetInt(Key_Star, 0);
        }
        set
        {
            SetInt(Key_Star, value);
        }
    }


    public static int Gold
    {
        get
        {
            return GetInt(Key_Gold, 0);
        }
        set
        {
            SetInt(Key_Gold, value);
        }
    }

    public static bool IsNewPlayer
    {
        get
        {
            return GetInt(Key_NewPlayer, 1) == 1;
        }
        set
        {
            SetInt(Key_NewPlayer, value ? 1 : 0);
        }
    }
    public static bool NeedGuide
    {
        get
        {
            return GetInt(Key_NeedGuide, 1) == 1;
        }
        set
        {
            SetInt(Key_NeedGuide, value ? 1 : 0);
        }
    }

    public static bool SoundEffectOn
    {
        get
        {
            return GetInt(Key_SoundEffectOn, 1) == 1;
        }
        set
        {
            SetInt(Key_SoundEffectOn, value ? 1 : 0);
        }
    }

    public static bool VibrationOn
    {
        get
        {
            return GetInt(Key_VibrationOn, 1) == 1;
        }
        set
        {
            SetInt(Key_VibrationOn, value ? 1 : 0);
        }
    }

    public static string GameProgress
    {
        get
        {
            return GetString(Key_GameProgress);
        }
        set
        {
            SetString(Key_GameProgress, value);
        }
    }
    public static string OpenId
    {
        get
        {
            return GetString(Key_OpenId);
        }
        set
        {
            SetString(Key_OpenId, value);
        }
    }
    public static string ClientId
    {
        get
        {
            var clientId = GetString(Key_ClientId);
            if (string.IsNullOrEmpty(clientId))
            {
                clientId = System.DateTime.Now.ToString("yyyyMMddHHmmss") + "_" + UnityEngine.Random.Range(1, 99999);
                SetString(Key_ClientId, clientId);
            }
            return clientId;
        }
    }


    public static int ItemBulbCount { get => GetInt(ItemBulb); internal set => SetInt(ItemBulb, value); }
    public static int ItemShuffleCount { get => GetInt(ItemShuffle); internal set => SetInt(ItemShuffle, value); }
    public static int ItemTurnCount { get => GetInt(ItemTurn); internal set => SetInt(ItemTurn, value); }
    public static int ItemMagnetCount { get => GetInt(ItemMagnet); internal set => SetInt(ItemMagnet, value); }
    public static int GetFreeItemCount { get => GetInt(KeyGetFreeItemCount); internal set => SetInt(KeyGetFreeItemCount, value); }
    public static float VolumeBgm { get => GetFloat(KeyVolumeBgm, 1); internal set => SetFloat(KeyVolumeBgm, value); }
    public static float VolumeEffect { get => GetFloat(KeyVolumeEffect, 1); internal set => SetFloat(KeyVolumeEffect, value); }
    public static string Channel { get => GetString(KeyChannel); internal set => SetString(KeyChannel, value); }
    public static int OpenSlotByShareCount { get => GetInt(KeyOpenSlotByShareCount); internal set => SetInt(KeyOpenSlotByShareCount, value); }

    private static void SetInt(string key, int value)
    {
        Platform.Instance.SetStorage(key, value);
    }
    private static int GetInt(string key, int defaultValue = 0)
    {
        return Platform.Instance.GetStorage<int>(key, defaultValue);
    }
    private static void SetFloat(string key, float value)
    {
        Platform.Instance.SetStorage(key, value);
    }
    private static float GetFloat(string key, float defaultValue = 0)
    {
        return Platform.Instance.GetStorage<float>(key, defaultValue);
    }
    private static void SetString(string key, string value)
    {
        Platform.Instance.SetStorage(key, value);
    }
    private static string GetString(string key, string defaultValue = "")
    {
        return Platform.Instance.GetStorage<string>(key, defaultValue);
    }
}